"txdate","pro_name","table_name","exception_code","exception_msg","create_time"
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:27:08.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:29:20.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:29:21.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:29:22.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:29:22.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:32:51.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:34:46.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:34:48.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:37:19.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:38:25.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:38:27.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:38:52.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:38:53.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:39:16.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:39:17.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:39:39.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:39:41.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:45:31.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:47:02.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:47:03.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:47:47.000
"",PRO_LOG,日志处理异常,-20010,"(p_pro_date=20240101,p_status=R,l_pro_code=PRO_ATTENDANCE_RESULT):ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 14:20:37.000
"20240101",PRO_ATTENDANCE_RESULT,????????,-20010,"0:ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 14:20:37.000
"",PRO_LOG,日志处理异常,-20010,"(p_pro_date=20231201,p_status=R,l_pro_code=PRO_ATTENDANCE_RESULT):ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:42:37.000
"20231201",PRO_ATTENDANCE_RESULT,????????,-20010,"0:ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:42:51.000
"",PRO_LOG,日志处理异常,-20010,"(p_pro_date=20231201,p_status=R,l_pro_code=PRO_ATTENDANCE_RESULT):ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:44:37.000
"20231201",PRO_ATTENDANCE_RESULT,????????,-20010,"0:ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:44:41.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: internal error code, arguments: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 09:30:37.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: internal error code, arguments: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 09:41:10.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:51:42.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:51:43.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:51:44.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:51:44.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:51:45.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:51:51.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 15:12:45.000
"20231201",PRO_ATTENDANCE_RESULT,更新年假病假,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-02 17:29:52.000
"20241201",PRO_ATTENDANCE_RESULT,插入ATTENDANCE_RESULT,-1476,"2:ORA-01476: 除数为 0",2024-01-02 17:30:45.000
"20231201",PRO_ATTENDANCE_RESULT,更新年假病假,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-02 17:39:21.000
"20231201",PRO_ATTENDANCE_RESULT,更新年假病假,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-02 17:39:23.000
"20231201",PRO_ATTENDANCE_RESULT,更新年假病假,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-02 17:44:55.000
"20231201",PRO_ATTENDANCE_RESULT,更新年假病假,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-02 17:44:57.000
"20231201",PRO_ATTENDANCE_RESULT,更新年假病假,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-02 17:45:40.000
"20231201",PRO_ATTENDANCE_RESULT,更新年假病假,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-02 17:45:41.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 09:25:36.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 09:38:23.000
"20231201",PRO_ATTENDANCE_RESULT,更新在岗系数,-600,"5:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 09:39:20.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 09:42:03.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 09:51:47.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 09:51:48.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 10:04:01.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 10:04:03.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 10:15:53.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 10:15:54.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 10:21:25.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 10:40:45.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 10:40:46.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 10:48:18.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 10:52:25.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 10:52:27.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 10:53:07.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 10:53:08.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 10:54:05.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 10:54:06.000
"20231201",PRO_LOG,日志处理异常,-20014,"(p_pro_date=20231201,p_status=R,l_pro_code=PRO_ATTENDANCE_RESULT):ORA-20014: 变量v_point和t_name做赋值必须唯一,以下程序行需要修改(477,527): t_name:='更新可休年假、病假、总年假、已休病假等';",2024-01-03 11:06:52.000
"20231201",PRO_ATTENDANCE_RESULT,程序开始,-20014,"0:ORA-20014: 变量v_point和t_name做赋值必须唯一,以下程序行需要修改(477,527): t_name:='更新可休年假、病假、总年假、已休病假等';",2024-01-03 11:06:52.000
"20231201",PRO_LOG,日志处理异常,-20014,"(p_pro_date=20231201,p_status=R,l_pro_code=PRO_ATTENDANCE_RESULT):ORA-20014: 变量v_point和t_name做赋值必须唯一,以下程序行需要修改(477,527): t_name:='更新可休年假、病假、总年假、已休病假等';",2024-01-03 11:06:53.000
"20231201",PRO_ATTENDANCE_RESULT,程序开始,-20014,"0:ORA-20014: 变量v_point和t_name做赋值必须唯一,以下程序行需要修改(477,527): t_name:='更新可休年假、病假、总年假、已休病假等';",2024-01-03 11:06:53.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:08:09.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:13:15.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:13:17.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:14:03.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:14:05.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:14:06.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:15:51.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:17:14.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:17:15.000
"",PRO_T_ZHJX_MONTH_JCJX,基础评价绩效计算更新,-937,"4:ORA-00937: 不是单组分组函数",2024-03-28 16:08:59.000
"20220930",PRO_T_ZHJX_ZDJY_GLPJ_YZ_KHLR,一支自营收入绩效_考核利润,-6502,ORA-06502: PL/SQL: 数字或值错误 :  字符串缓冲区太小,2022-11-14 11:18:23.000
"20220930",PRO_T_ZHJX_ZDJY_GLPJ_YZ_KHLR,一支自营收入绩效_考核利润,-922,ORA-00922: 选项缺失或无效,2022-11-14 11:21:04.000
"20220930",PRO_T_ZHJX_ZDJY_GLPJ_YZ_KHLR,一支自营收入绩效_考核利润,-922,ORA-00922: 选项缺失或无效,2022-11-14 11:26:28.000
"20220930",PRO_T_ZHJX_ZDJY_GLPJ_YZ_KHLR,一支自营收入绩效_考核利润,-6535,ORA-06535: EXECUTE IMMEDIATE 中的语句字符串为 NULL 或长度为零,2022-11-14 11:31:12.000
"20220930",PRO_T_ZHJX_ZDJY_GLPJ_YZ_KHLR,一支自营收入绩效_考核利润,-6535,ORA-06535: EXECUTE IMMEDIATE 中的语句字符串为 NULL 或长度为零,2022-11-14 11:32:46.000
"20220930",PRO_T_ZHJX_ZDJY_GLPJ_YZ_KHLR,一支自营收入绩效_考核利润,-6535,ORA-06535: EXECUTE IMMEDIATE 中的语句字符串为 NULL 或长度为零,2022-11-14 11:33:26.000
"20220930",PRO_T_ZHJX_ZDJY_GLPJ_YZ_KHLR,一支自营收入绩效_考核利润,-6535,ORA-06535: EXECUTE IMMEDIATE 中的语句字符串为 NULL 或长度为零,2022-11-14 11:35:43.000
"20220930",PRO_T_ZHJX_ZDJY_GLPJ_YZ_KHLR,一支自营收入绩效_考核利润,-6535,ORA-06535: EXECUTE IMMEDIATE 中的语句字符串为 NULL 或长度为零,2022-11-14 11:45:17.000
"20220930",PRO_T_ZHJX_ZDJY_GLPJ_YZ_KHLR,一支自营收入绩效_考核利润,-6535,ORA-06535: EXECUTE IMMEDIATE 中的语句字符串为 NULL 或长度为零,2022-11-14 11:46:27.000
"20220930",PRO_T_ZHJX_ZDJY_GLPJ_YZ_KHLR,一支自营收入绩效_考核利润,-6535,ORA-06535: EXECUTE IMMEDIATE 中的语句字符串为 NULL 或长度为零,2022-11-14 11:48:31.000
"20220930",PRO_T_ZHJX_ZDJY_GLPJ_YZ_KHLR,一支自营收入绩效_考核利润,-6535,ORA-06535: EXECUTE IMMEDIATE 中的语句字符串为 NULL 或长度为零,2022-11-14 11:48:36.000
"20220930",PRO_T_ZHJX_ZDJY_GLPJ_YZ_BLDKQCZ,一支自营收入绩效_不良贷款清处置,-1400,"ORA-01400: 无法将 NULL 插入 (""SCOTT"".""T_ZHJX_ZDJY_GLPJ_YZ_BLDKQCZ"".""BLDKQCZ_JBF"")",2022-11-15 15:56:17.000
"20220930",PRO_T_ZHJX_ZDJY_GLPJ_YZ_TZYH,一支自营收入绩效_投资银行,-1400,"ORA-01400: 无法将 NULL 插入 (""SCOTT"".""T_ZHJX_ZDJY_GLPJ_YZ_TZYH"".""JBF"")",2022-11-19 11:09:47.000
"20220930",PRO_T_ZHJX_ZDJY_GLPJ_YZ_TZYH,一支自营收入绩效_投资银行,-1400,"ORA-01400: 无法将 NULL 插入 (""SCOTT"".""T_ZHJX_ZDJY_GLPJ_YZ_TZYH"".""JBF"")",2022-11-19 11:12:51.000
"20220930",PRO_T_ZHJX_ZDJY_GLPJ_YZ_KHLR,一支自营收入绩效_考核利润,-6502,ORA-06502: PL/SQL: 数字或值错误 :  字符串缓冲区太小,2022-11-14 11:18:23.000
"20220930",PRO_T_ZHJX_ZDJY_GLPJ_YZ_KHLR,一支自营收入绩效_考核利润,-922,ORA-00922: 选项缺失或无效,2022-11-14 11:21:04.000
"20220930",PRO_T_ZHJX_ZDJY_GLPJ_YZ_KHLR,一支自营收入绩效_考核利润,-922,ORA-00922: 选项缺失或无效,2022-11-14 11:26:28.000
"20220930",PRO_T_ZHJX_ZDJY_GLPJ_YZ_KHLR,一支自营收入绩效_考核利润,-6535,ORA-06535: EXECUTE IMMEDIATE 中的语句字符串为 NULL 或长度为零,2022-11-14 11:31:12.000
"20220930",PRO_T_ZHJX_ZDJY_GLPJ_YZ_KHLR,一支自营收入绩效_考核利润,-6535,ORA-06535: EXECUTE IMMEDIATE 中的语句字符串为 NULL 或长度为零,2022-11-14 11:32:46.000
"20220930",PRO_T_ZHJX_ZDJY_GLPJ_YZ_KHLR,一支自营收入绩效_考核利润,-6535,ORA-06535: EXECUTE IMMEDIATE 中的语句字符串为 NULL 或长度为零,2022-11-14 11:33:26.000
"20220930",PRO_T_ZHJX_ZDJY_GLPJ_YZ_KHLR,一支自营收入绩效_考核利润,-6535,ORA-06535: EXECUTE IMMEDIATE 中的语句字符串为 NULL 或长度为零,2022-11-14 11:35:43.000
"20220930",PRO_T_ZHJX_ZDJY_GLPJ_YZ_KHLR,一支自营收入绩效_考核利润,-6535,ORA-06535: EXECUTE IMMEDIATE 中的语句字符串为 NULL 或长度为零,2022-11-14 11:45:17.000
"20220930",PRO_T_ZHJX_ZDJY_GLPJ_YZ_KHLR,一支自营收入绩效_考核利润,-6535,ORA-06535: EXECUTE IMMEDIATE 中的语句字符串为 NULL 或长度为零,2022-11-14 11:46:27.000
"20220930",PRO_T_ZHJX_ZDJY_GLPJ_YZ_KHLR,一支自营收入绩效_考核利润,-6535,ORA-06535: EXECUTE IMMEDIATE 中的语句字符串为 NULL 或长度为零,2022-11-14 11:48:31.000
"20220930",PRO_T_ZHJX_ZDJY_GLPJ_YZ_KHLR,一支自营收入绩效_考核利润,-6535,ORA-06535: EXECUTE IMMEDIATE 中的语句字符串为 NULL 或长度为零,2022-11-14 11:48:36.000
"20220930",PRO_T_ZHJX_ZDJY_GLPJ_YZ_BLDKQCZ,一支自营收入绩效_不良贷款清处置,-1400,"ORA-01400: 无法将 NULL 插入 (""SCOTT"".""T_ZHJX_ZDJY_GLPJ_YZ_BLDKQCZ"".""BLDKQCZ_JBF"")",2022-11-15 15:56:17.000
"20220930",PRO_T_ZHJX_ZDJY_GLPJ_YZ_TZYH,一支自营收入绩效_投资银行,-1400,"ORA-01400: 无法将 NULL 插入 (""SCOTT"".""T_ZHJX_ZDJY_GLPJ_YZ_TZYH"".""JBF"")",2022-11-19 11:09:47.000
"20220930",PRO_T_ZHJX_ZDJY_GLPJ_YZ_TZYH,一支自营收入绩效_投资银行,-1400,"ORA-01400: 无法将 NULL 插入 (""SCOTT"".""T_ZHJX_ZDJY_GLPJ_YZ_TZYH"".""JBF"")",2022-11-19 11:12:51.000
"20220930",PRO_T_ZHJX_ZDJY_YZ_SXFJYJJSR,一支自营收入绩效_手续费及佣金净收入,-1476,ORA-01476: 除数为 0,2022-11-23 11:08:02.000
"",PRO_LOG,日志处理异常,-20012,"(p_pro_date=20231201,p_status=R,l_pro_code=PRO_ATTENDANCE_RESULT):ORA-20012: 加工程序的第3个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:27:03.000
"20231201",PRO_ATTENDANCE_RESULT,程序开始,-20012,"0:ORA-20012: 加工程序的第3个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:27:03.000
"",PRO_LOG,日志处理异常,-20012,"(p_pro_date=20231201,p_status=R,l_pro_code=PRO_ATTENDANCE_RESULT):ORA-20012: 加工程序的第3个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:28:09.000
"20231201",PRO_ATTENDANCE_RESULT,程序开始,-20012,"0:ORA-20012: 加工程序的第3个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:28:09.000
"",PRO_LOG,日志处理异常,-20012,"(p_pro_date=20231201,p_status=R,l_pro_code=PRO_ATTENDANCE_RESULT):ORA-20012: 加工程序的第3个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:28:25.000
"20231201",PRO_ATTENDANCE_RESULT,程序开始,-20012,"0:ORA-20012: 加工程序的第3个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:28:25.000
"",PRO_LOG,日志处理异常,-20012,"(p_pro_date=20231201,p_status=R,l_pro_code=PRO_ATTENDANCE_RESULT):ORA-20012: 加工程序的第3个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:28:26.000
"20231201",PRO_ATTENDANCE_RESULT,程序开始,-20012,"0:ORA-20012: 加工程序的第3个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:28:26.000
"20231201",PRO_LOG,????????????,-20010,"(p_pro_date=20231201,p_status=R,l_pro_code=PRO_ATTENDANCE_RESULT):ORA-20010: ????????????1????????????????????,??????????,??????????",2024-01-02 16:51:52.000
"20231201",PRO_ATTENDANCE_RESULT,????????,-20010,"0:ORA-20010: ????????????1????????????????????,??????????,??????????",2024-01-02 16:52:14.000
"20231201",PRO_LOG,????????????,-20010,"(p_pro_date=20231201,p_status=R,l_pro_code=PRO_ATTENDANCE_RESULT):ORA-20010: ????????????1????????????????????,??????????,??????????",2024-01-02 16:56:28.000
"20231201",PRO_ATTENDANCE_RESULT,????????,-20010,"0:ORA-20010: ????????????1????????????????????,??????????,??????????",2024-01-02 16:56:28.000
"20231201",PRO_LOG,????????????,-20010,"(p_pro_date=20231201,p_status=R,l_pro_code=PRO_ATTENDANCE_RESULT):ORA-20010: ????????????1????????????????????,??????????,??????????",2024-01-02 16:56:36.000
"20231201",PRO_ATTENDANCE_RESULT,????????,-20010,"0:ORA-20010: ????????????1????????????????????,??????????,??????????",2024-01-02 16:56:36.000
"20231201",PRO_LOG,日志处理异常,-20010,"(p_pro_date=20231201,p_status=R,l_pro_code=PRO_ATTENDANCE_RESULT):ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:59:04.000
"20231201",PRO_ATTENDANCE_RESULT,????????,-20010,"0:ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:59:04.000
"20231201",PRO_LOG,日志处理异常,-20010,"(p_pro_date=20231201,p_status=R,l_pro_code=PRO_ATTENDANCE_RESULT):ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:59:06.000
"20231201",PRO_ATTENDANCE_RESULT,????????,-20010,"0:ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:59:06.000
"20231201",PRO_LOG,日志处理异常,-20010,"(p_pro_date=20231201,p_status=R,l_pro_code=PRO_ATTENDANCE_RESULT):ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:59:06.000
"20231201",PRO_ATTENDANCE_RESULT,????????,-20010,"0:ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:59:06.000
"",PRO_LOG,日志处理异常,-20010,"(p_pro_date=20241201,p_status=R,l_pro_code=PRO_ATTENDANCE_RESULT):ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 15:37:30.000
"20241201",PRO_ATTENDANCE_RESULT,????????,-20010,"0:ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 15:37:30.000
"",PRO_LOG,日志处理异常,-20010,"(p_pro_date=20231201,p_status=R,l_pro_code=PRO_ATTENDANCE_RESULT):ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 15:38:46.000
"20231201",PRO_ATTENDANCE_RESULT,????????,-20010,"0:ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 15:38:46.000
"",PRO_LOG,日志处理异常,-20010,"(p_pro_date=20231201,p_status=R,l_pro_code=PRO_ATTENDANCE_RESULT):ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 15:39:29.000
"20231201",PRO_ATTENDANCE_RESULT,????????,-20010,"0:ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 15:39:29.000
"",PRO_LOG,日志处理异常,-20010,"(p_pro_date=20231201,p_status=R,l_pro_code=PRO_ATTENDANCE_RESULT):ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 15:41:52.000
"20231201",PRO_ATTENDANCE_RESULT,????????,-20010,"0:ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 15:41:52.000
"",PRO_LOG,日志处理异常,-20010,"(p_pro_date=20241201,p_status=R,l_pro_code=PRO_ATTENDANCE_RESULT):ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 15:43:04.000
"20241201",PRO_ATTENDANCE_RESULT,????????,-20010,"0:ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 15:43:04.000
"",PRO_LOG,日志处理异常,-20010,"(p_pro_date=20231201,p_status=R,l_pro_code=PRO_ATTENDANCE_RESULT):ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 15:43:09.000
"20231201",PRO_ATTENDANCE_RESULT,????????,-20010,"0:ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 15:43:09.000
"",PRO_LOG,日志处理异常,-20012,"(p_pro_date=20231201,p_status=R,l_pro_code=PRO_ATTENDANCE_RESULT):ORA-20012: 加工程序的第3个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 15:47:08.000
"20231201",PRO_ATTENDANCE_RESULT,程序开始,-20012,"0:ORA-20012: 加工程序的第3个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 15:47:08.000
"",PRO_LOG,日志处理异常,-20012,"(p_pro_date=20231201,p_status=R,l_pro_code=PRO_ATTENDANCE_RESULT):ORA-20012: 加工程序的第3个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:00:25.000
"20231201",PRO_ATTENDANCE_RESULT,程序开始,-20012,"0:ORA-20012: 加工程序的第3个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:00:25.000
"",PRO_LOG,日志处理异常,-20012,"(p_pro_date=20231201,p_status=R,l_pro_code=PRO_ATTENDANCE_RESULT):ORA-20012: 加工程序的第3个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:30:34.000
"20231201",PRO_ATTENDANCE_RESULT,程序开始,-20012,"0:ORA-20012: 加工程序的第3个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:30:34.000
"",PRO_LOG,日志处理异常,-20010,"(p_pro_date=20231201,p_status=R,l_pro_code=PRO_ATTENDANCE_RESULT):ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:36:49.000
"20231201",PRO_ATTENDANCE_RESULT,????????,-20010,"0:ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:36:49.000
"",PRO_LOG,日志处理异常,-20010,"(p_pro_date=20231201,p_status=R,l_pro_code=PRO_ATTENDANCE_RESULT):ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:37:26.000
"20231201",PRO_ATTENDANCE_RESULT,????????,-20010,"0:ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:37:26.000
"",PRO_LOG,日志处理异常,-20010,"(p_pro_date=20231201,p_status=R,l_pro_code=PRO_ATTENDANCE_RESULT):ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:39:22.000
"20231201",PRO_ATTENDANCE_RESULT,????????,-20010,"0:ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:39:22.000
"",PRO_LOG,日志处理异常,-20010,"(p_pro_date=20231201,p_status=R,l_pro_code=PRO_ATTENDANCE_RESULT):ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:40:44.000
"20231201",PRO_ATTENDANCE_RESULT,????????,-20010,"0:ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:40:44.000
"",PRO_LOG,日志处理异常,-20010,"(p_pro_date=20231201,p_status=R,l_pro_code=PRO_ATTENDANCE_RESULT):ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:42:05.000
"20231201",PRO_ATTENDANCE_RESULT,????????,-20010,"0:ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:42:14.000
"20231201",PRO_LOG,日志处理异常,-20010,"(p_pro_date=20231201,p_status=R,l_pro_code=PRO_ATTENDANCE_RESULT):ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:59:06.000
"20231201",PRO_ATTENDANCE_RESULT,????????,-20010,"0:ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:59:06.000
"20231201",PRO_LOG,日志处理异常,-20010,"(p_pro_date=20231201,p_status=R,l_pro_code=PRO_ATTENDANCE_RESULT):ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:59:07.000
"20231201",PRO_ATTENDANCE_RESULT,????????,-20010,"0:ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:59:07.000
"20231201",PRO_LOG,日志处理异常,-20010,"(p_pro_date=20231201,p_status=R,l_pro_code=PRO_ATTENDANCE_RESULT):ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:59:07.000
"20231201",PRO_ATTENDANCE_RESULT,????????,-20010,"0:ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 16:59:07.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-02 17:03:37.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: internal error code, arguments: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-02 17:08:21.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: internal error code, arguments: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-02 17:08:45.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假病假总年假已休病假,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-02 17:11:08.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假病假总年假已休病假,-600,"6:ORA-00600: internal error code, arguments: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-02 17:12:06.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假病假总年假已休病假,-600,"6:ORA-00600: internal error code, arguments: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-02 17:13:00.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假病假总年假已休病假,-600,"6:ORA-00600: internal error code, arguments: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-02 17:13:10.000
"20231201",PRO_ATTENDANCE_RESULT,更新年假病假,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-02 17:15:28.000
"20231201",PRO_ATTENDANCE_RESULT,更新年假病假,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-02 17:15:30.000
"20231201",PRO_ATTENDANCE_RESULT,更新年假病假,-600,"6:ORA-00600: internal error code, arguments: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-02 17:19:23.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:17:49.000
"",PRO_LOG,日志处理异常,-20010,"(p_pro_date=20231201,p_status=R,l_pro_code=PRO_ATTENDANCE_RESULT):ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 15:23:21.000
"20231201",PRO_ATTENDANCE_RESULT,????????,-20010,"0:ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 15:23:21.000
"",PRO_LOG,日志处理异常,-20010,"(p_pro_date=20231201,p_status=R,l_pro_code=PRO_ATTENDANCE_RESULT):ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 15:29:42.000
"20231201",PRO_ATTENDANCE_RESULT,????????,-20010,"0:ORA-20010: 加工程序的第1个固定代码区域被篡改,请恢复如初,包括注释。",2024-01-02 15:29:42.000
"20231201",PRO_ATTENDANCE_RESULT,更新年假病假,-600,"6:ORA-00600: internal error code, arguments: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-02 17:20:54.000
"20231201",PRO_ATTENDANCE_RESULT,更新年假病假,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-02 17:31:04.000
"20231201",PRO_ATTENDANCE_RESULT,更新年假病假,-600,"6:ORA-00600: internal error code, arguments: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-02 17:31:27.000
"20231201",PRO_ATTENDANCE_RESULT,更新年假病假,-600,"6:ORA-00600: internal error code, arguments: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-02 17:32:13.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:17:49.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:17:50.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:17:52.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:18:38.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:18:40.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:21:10.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:21:11.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:22:15.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:22:17.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:23:04.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:23:05.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 11:27:07.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 15:14:51.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 15:16:09.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 15:16:10.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 15:17:29.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 15:17:30.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 15:25:16.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 15:28:01.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 15:28:03.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: internal error code, arguments: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 15:30:43.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 15:33:26.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 15:41:02.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 15:41:03.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 15:41:04.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-600,"6:ORA-00600: 内部错误代码, 参数: [qcsfbdnp:1], [], [], [], [], [], [], [], [], [], [], []",2024-01-03 15:46:21.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-937,"6:ORA-00937: 不是单组分组函数",2024-01-03 15:48:28.000
"20231201",PRO_ATTENDANCE_RESULT,更新可休年假、病假、总年假、已休病假等,-937,"6:ORA-00937: 不是单组分组函数",2024-01-03 15:48:29.000
"02280854",PRO_USER_INFO_MODIFY_RT,用户信息变更批处理,-904,"ORA-00904: ""S"".""USER_ID"": 标识符无效",2024-02-28 08:54:32.000
"02280907",PRO_USER_INFO_MODIFY_RT,用户信息变更批处理,-904,"ORA-00904: ""S"".""USER_ID"": 标识符无效",2024-02-28 09:07:02.000
"02280922",PRO_USER_INFO_MODIFY_RT,用户信息变更批处理,-904,"ORA-00904: ""S"".""USER_ID"": 标识符无效",2024-02-28 09:22:08.000
"02280854",PRO_USER_INFO_MODIFY_RT,用户信息变更批处理,-904,"ORA-00904: ""S"".""USER_ID"": invalid identifier",2024-02-28 10:14:42.000
"02280854",PRO_USER_INFO_MODIFY_RT,用户信息变更批处理,-904,"ORA-00904: ""S"".""USER_ID"": invalid identifier",2024-02-28 10:15:13.000
"02280854",PRO_USER_INFO_MODIFY_RT,用户信息变更批处理,-904,"ORA-00904: ""S"".""USER_ID"": invalid identifier",2024-02-28 10:19:45.000
"02280854",PRO_USER_INFO_MODIFY_RT,??????????????????,-904,"ORA-00904: ""S"".""USER_ID"": invalid identifier",2024-02-28 10:26:59.000
"02280854",PRO_USER_INFO_MODIFY_RT,??????????????????,"100",ORA-01403: no data found,2024-02-28 10:55:21.000
"02280854",PRO_USER_INFO_MODIFY_RT,??????????????????,"100",ORA-01403: no data found,2024-02-28 11:01:34.000
"02280854",PRO_USER_INFO_MODIFY_RT,??????????????????,"100",ORA-01403: no data found,2024-02-28 11:07:34.000
"02280854",PRO_USER_INFO_MODIFY_RT,??????????????????,"100",ORA-01403: no data found,2024-02-28 11:11:04.000
"20231231",PRO_LOG,日志处理异常,-20003,"(p_pro_date=20231231,p_status=R,l_pro_code=PRO_T_ZHJX_LINE_CREATES):ORA-20003: 目标表T_ZHJX_LINE_CREATES不存在!",2024-03-21 09:36:38.000
"",PRO_LOG,日志处理异常,-20003,"(p_pro_date=,p_status=F,l_pro_code=PRO_T_ZHJX_LINE_CREATES):ORA-20003: 目标表T_ZHJX_LINE_CREATES不存在!",2024-03-21 09:36:38.000
"20231231",PRO_LOG,日志处理异常,-20003,"(p_pro_date=20231231,p_status=R,l_pro_code=PRO_T_ZHJX_LINE_CREATES):ORA-20003: 目标表T_ZHJX_LINE_CREATES不存在!",2024-03-21 09:37:55.000
"",PRO_LOG,日志处理异常,-20003,"(p_pro_date=,p_status=F,l_pro_code=PRO_T_ZHJX_LINE_CREATES):ORA-20003: 目标表T_ZHJX_LINE_CREATES不存在!",2024-03-21 09:37:55.000
"20231231",PRO_LOG,日志处理异常,-20003,"(p_pro_date=20231231,p_status=R,l_pro_code=PRO_T_ZHJX_LINE_CREATES):ORA-20003: 目标表T_ZHJX_LINE_CREATES不存在!",2024-03-21 09:38:34.000
"",PRO_T_ZHJX_MONTH_JCJX,基础评价绩效计算更新,-937,"4:ORA-00937: 不是单组分组函数",2024-03-28 16:57:57.000
"",PRO_T_ZHJX_MONTH_JCJX,基础评价绩效计算更新,-937,"4:ORA-00937: 不是单组分组函数",2024-03-29 10:11:02.000
"20240520",PRO_T_ZHJX_XCFF_REPORT,生成薪资发放明细,-937,"0:ORA-00937: 不是单组分组函数",2024-05-20 19:41:20.000
"20240410",PRO_T_ZHJX_DETAILS,综合绩效贡献绩效计算更新,-6502,"0:ORA-06502: PL/SQL: 数字或值错误 :  字符串缓冲区太小",2024-04-10 10:00:33.000
"20240520",PRO_T_ZHJX_XCFF_REPORT,生成薪资发放明细,-937,"0:ORA-00937: 不是单组分组函数",2024-05-20 19:58:21.000
"20240520",PRO_T_ZHJX_XCFF_REPORT,生成薪资发放明细,-937,"0:ORA-00937: 不是单组分组函数",2024-05-20 19:58:25.000
"20240520",PRO_T_ZHJX_XCFF_REPORT,生成薪资发放明细,-937,"0:ORA-00937: 不是单组分组函数",2024-05-20 20:02:46.000
"20240520",PRO_T_ZHJX_XCFF_REPORT,生成薪资发放明细,"100","0:ORA-01403: 未找到任何数据",2024-05-20 20:07:24.000
"20240606",PRO_T_ZHJX_XCFF_REPORT,生成薪资发放明细,"100","0:ORA-01403: 未找到任何数据",2024-06-06 14:57:23.000
"",PRO_LOG,日志处理异常,-20003,"(p_pro_date=,p_status=F,l_pro_code=PRO_T_ZHJX_LINE_CREATES):ORA-20003: 目标表T_ZHJX_LINE_CREATES不存在!",2024-03-21 09:38:34.000
"",PRO_T_ZHJX_MONTH_JCJX,基础评价绩效计算更新,-937,"4:ORA-00937: 不是单组分组函数",2024-03-28 16:16:16.000
"20231130",PRO_T_ZHJX_QA_EVAL,综合绩效季度贡献绩效工资计算更新,-30926,"6:ORA-30926: 无法在源表中获得一组稳定的行",2024-03-29 15:30:35.000
"20231130",PRO_T_ZHJX_QA_EVAL,综合绩效季度贡献绩效工资计算更新,-30926,"6:ORA-30926: 无法在源表中获得一组稳定的行",2024-03-29 15:31:00.000
"20240618",PRO_T_ZHJX_XCFF_REPORT,生成薪资发放明细,"100","0:ORA-01403: 未找到任何数据",2024-06-18 18:55:44.000
"20241101",PRO_ATTENDANCE_RESULT,插入ATTENDANCE_RESULT,-1476,"2:ORA-01476: 除数为 0",2024-03-22 16:40:03.000
"20231130",PRO_T_ZHJX_QA_EVAL,综合绩效季度贡献绩效工资计算更新,-30926,"6:ORA-30926: 无法在源表中获得一组稳定的行",2024-03-29 12:43:07.000
"20240520",PRO_T_ZHJX_XCFF_REPORT,生成薪资发放明细,-937,"0:ORA-00937: 不是单组分组函数",2024-05-20 19:39:28.000
"20240520",PRO_T_ZHJX_XCFF_REPORT,生成薪资发放明细,-937,"0:ORA-00937: 不是单组分组函数",2024-05-20 19:46:46.000
"20240520",PRO_T_ZHJX_XCFF_REPORT,生成薪资发放明细,-937,"0:ORA-00937: 不是单组分组函数",2024-05-20 19:47:11.000
"20240520",PRO_T_ZHJX_XCFF_REPORT,生成薪资发放明细,-937,"0:ORA-00937: 不是单组分组函数",2024-05-20 19:47:16.000
"20240520",PRO_T_ZHJX_XCFF_REPORT,生成薪资发放明细,-937,"0:ORA-00937: 不是单组分组函数",2024-05-20 20:03:08.000
"20240618",PRO_T_ZHJX_XCFF_REPORT,生成薪资发放明细,"100","0:ORA-01403: 未找到任何数据",2024-06-18 18:29:54.000
"20240618",PRO_T_ZHJX_XCFF_REPORT,生成薪资发放明细,"100","0:ORA-01403: 未找到任何数据",2024-06-18 18:30:58.000
"20240618",PRO_T_ZHJX_XCFF_REPORT,生成薪资发放明细,"100","0:ORA-01403: 未找到任何数据",2024-06-18 18:51:16.000
"20231130",PRO_T_ZHJX_MONTH_JCJX,基础评价绩效计算更新,-937,"4:ORA-00937: not a single-group group function",2024-03-29 10:17:23.000
"20240410",PRO_T_ZHJX_DETAILS,综合绩效贡献绩效计算更新,-6502,"0:ORA-06502: PL/SQL: 数字或值错误 :  字符串缓冲区太小",2024-04-10 09:09:43.000
"20240410",PRO_T_ZHJX_DETAILS,综合绩效贡献绩效计算更新,-6502,"0:ORA-06502: PL/SQL: 数字或值错误 :  字符串缓冲区太小",2024-04-10 09:49:37.000
"20231130",PRO_T_ZHJX_QA_EVAL,综合绩效季度贡献绩效工资计算更新,-30926,"6:ORA-30926: 无法在源表中获得一组稳定的行",2024-03-29 12:43:07.000
"",PRO_T_ZHJX_MONTH_JCJX,基础评价绩效计算更新,-937,"4:ORA-00937: 不是单组分组函数",2024-03-25 11:20:32.000
"",PRO_T_ZHJX_MONTH_JCJX,基础评价绩效计算更新,-937,"4:ORA-00937: 不是单组分组函数",2024-03-25 11:24:29.000
"",PRO_T_ZHJX_MONTH_JCJX,基础评价绩效计算更新,-937,"4:ORA-00937: 不是单组分组函数",2024-03-28 16:12:26.000
"",PRO_T_ZHJX_MONTH_JCJX,基础评价绩效计算更新,-937,"4:ORA-00937: 不是单组分组函数",2024-03-28 16:13:48.000
"",PRO_T_ZHJX_MONTH_JCJX,基础评价绩效计算更新,-937,"4:ORA-00937: 不是单组分组函数",2024-03-29 09:45:12.000
"20231130",PRO_T_ZHJX_QA_EVAL,综合绩效季度贡献绩效工资计算更新,-30926,"6:ORA-30926: 无法在源表中获得一组稳定的行",2024-03-29 09:45:15.000
"",PRO_T_ZHJX_MONTH_JCJX,基础评价绩效计算更新,-937,"4:ORA-00937: 不是单组分组函数",2024-03-29 10:02:14.000
"20231130",PRO_T_ZHJX_QA_EVAL,综合绩效季度贡献绩效工资计算更新,-3113,"1:ORA-03113: 通信通道的文件结尾",2024-03-29 10:09:39.000
"20231130",PRO_T_ZHJX_QA_EVAL,综合绩效季度贡献绩效工资计算更新,-30926,"6:ORA-30926: unable to get a stable set of rows in the source tables",2024-03-29 15:42:16.000
"20231130",PRO_T_ZHJX_QA_EVAL,综合绩效季度贡献绩效工资计算更新,-30926,"6:ORA-30926: unable to get a stable set of rows in the source tables",2024-03-29 15:42:23.000
"20240410",PRO_T_ZHJX_DETAILS,综合绩效贡献绩效计算更新,-6502,"0:ORA-06502: PL/SQL: 数字或值错误 :  字符串缓冲区太小",2024-04-10 10:00:56.000
"20240731",PRO_BL_DATA_DELETE_PRE,T_TMP_TXKHGSB_BL,-942,ORA-00942: 表或视图不存在,2024-08-07 15:53:34.000
"20240731",PRO_BL_DATA_DELETE_PRE,T_TMP_TXKHGSB_BL,-942,ORA-00942: 表或视图不存在,2024-08-07 16:01:57.000
"20240731",PRO_BL_DATA_DELETE_PRE,T_TMP_TXKHGSB_BL,-942,ORA-00942: 表或视图不存在,2024-08-07 16:09:08.000
"20240731",PRO_BL_DATA_DELETE_PRE,T_TMP_TXKHGSB_BL,-942,ORA-00942: 表或视图不存在,2024-08-07 15:50:09.000
