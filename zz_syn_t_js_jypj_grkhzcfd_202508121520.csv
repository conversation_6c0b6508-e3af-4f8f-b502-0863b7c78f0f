"summ_date","stats_dt","inst_type","inst_up_no","inst_up_name","inst_no","inst_name","stats_prd_cd","org_hrcy_cd","org_attr_cd","location_attr_cd","cust_qty","cust_all_asst","cust_org_asst","zero_cust_qty","zero_to_yiwan_cust_qty","zero_to_yiwan_all_amt","zero_to_yiwan_org_amt","yiwan_to_wuwan_cust_qty","yiwan_to_wuwan_all_amt","yiwan_to_wuwan_org_amt","wuwan_to_shiwan_cust_qty","wuwan_to_shiwan_all_amt","wuwan_to_shiwan_org_amt","shiwan_to_ershiwan_cust_qty","shiwan_to_ershiwan_all_amt","shiwan_to_ershiwan_org_amt","ershiwan_to_sanshiwan_cust_qty","ershiwan_to_sanshiwan_all_amt","ershiwan_to_sanshiwan_org_amt","sanshiwan_to_sishiwan_cust_qty","sanshiwan_to_sishiwan_all_amt","sanshiwan_to_sishiwan_org_amt","sishiwan_to_wushiwan_cust_qty","sishiwan_to_wushiwan_all_amt","sishiwan_to_wushiwan_org_amt","wushiwan_to_yibaiwan_cust_qty","wushiwan_to_yibaiwan_all_amt","wushiwan_to_yibaiwan_org_amt","yibaiwan_to_erbaiwan_cust_qty","yibaiwan_to_erbaiwan_all_amt","yibaiwan_to_erbaiwan_org_amt","erbaiwan_to_sanbaiwan_cust_qty","erbaiwan_to_sanbaiwan_all_amt","erbaiwan_to_sanbaiwan_org_amt","sanbaiwan_to_sibaiwan_cust_qty","sanbaiwan_to_sibaiwan_all_amt","sanbaiwan_to_sibaiwan_org_amt","sibaiwan_to_wubaiwan_cust_qty","sibaiwan_to_wubaiwan_all_amt","sibaiwan_to_wubaiwan_org_amt","wubaiwan_to_liubaiwan_cust_qty","wubaiwan_to_liubaiwan_all_amt","wubaiwan_to_liubaiwan_org_amt","liubaiwan_to_yiqianwan_cqty","liubaiwan_to_yiqianwan_all_amt","liubaiwan_to_yiqianwan_org_amt","yiqianwan_to_wuqianwan_cqty","yiqianwan_to_wuqianwan_all_amt","yiqianwan_to_wuqianwan_org_amt","wuqianwan_to_yiyi_cust_qty","wuqianwan_to_yiyi_all_amt","wuqianwan_to_yiyi_org_amt","yiyi_last_cust_qty","yiyi_last_all_amt","yiyi_last_org_amt","bds_etl_job_dt","syn_load_date","numb"
