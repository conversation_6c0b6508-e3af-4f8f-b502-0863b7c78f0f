"dicttypeid","dicttypename","parentid","up_seq","rank"
operCycle,,fileConf,.operCycle.,2
ORG_InstLvl,,"",.ORG_InstLvl.,1
JXFP_CPBHJCBZ,,"",.JXFP_CPBHJCBZ.,1
JXFP_FPFS,,"",.JXFP_FPFS.,1
chkType,,taskConf,.chkType.,2
chkFlag,,taskConf,.chkFlag.,2
execFlag,,taskProc,.execFlag.,2
TYPE_NAME,,"",.typeName.,1
execCycle,,taskProc,.execCycle.,2
BC_SYS_ATTR,区域属性,"",.BC_SYS_ATTR.,1
BC_SYS_PERMIT_FLAG,是否允许该外系统接入,"",.BC_SYS_PERMIT_FLAG.,1
BC_RGTTLR_TLRSTAT,操作员状态,"",.BC_RGTTLR_TLRSTAT.,1
BC_ROLE_FLAG,角色标志,"",.BC_ROLE_FLAG.,1
dict_custGrade,Customer Grade,"",.dict_custGrade.,1
dict_match,For Regular Expression,"",.dict_match.,1
dict_method,method方法,"",.dict_method.,1
Educationalbak,学历,"",.EducationalbakB,1
BC_SYSPMT_SYSONOFF,系统开关,"",.BC_SYSPMT_SYSONOFF.,1
BC_SYS_STATE,外系统状态,"",.BC_SYS_STATE.,1
BC_SYS_TYPE,外系统类型,"",.BC_SYS_TYPE.,1
BC_TLR_CAFLAG,柜员统一认证标志,"",.BC_TLR_CAFLAG.,1
BC_TLR_CONTRATYPE,合同期限类型,"",.BC_TLR_CONTRATYPE.,1
BC_TLR_ENABLEFLAG,操作员生效(启用)标志,"",.BC_TLR_ENABLEFLAG.,1
KP_KH_TYPE,,"",.KP_KH_TYPE.,1
状态类型,,"",.状态类型.,1
KHJL_CP,,"",.KHJL_CP.,1
ZT_TYPE,,"",.ZT_TYPE.,1
BC_PARA_PSTATE,参数状态,"",.BC_PARA_PSTATE.,1
BC_PARA_PTYPE,范围参数标识,"",.BC_PARA_PTYPE.,1
country,Country,"",.country.,1
Deal_Type,交易类型,"",.Deal_Type.,1
Dept_Type,所属部门类型,"",.Dept_Type.,1
BC_HAN_TYPE,处理类型,"",.BC_HAN_TYPE.,1
BC_AUTHORIZE_FLAG,授权标识,"",.BC_AUTHORIZE_FLAG.,1
BC_AUTHREL_NETFLAG,互联网标识,"",.BC_AUTHREL_NETFLAG.,1
BC_BLG_MANG_LVL,所属管理层级别,"",.BC_BLG_MANG_LVL.,1
BC_CHGINSTDTL_HANDPROC,处理流程,"",.BC_CHGINSTDTL_HANDPROC.,1
BC_CHGINSTDTL_HANDSTATE,处理状态,"",.BC_CHGINSTDTL_HANDSTATE.,1
BC_CHGINSTDTL_OPERTYPE,操作类型,"",.BC_CHGINSTDTL_OPERTYPE.,1
BC_CHNL_ONOFF,渠道开关,"",.BC_CHNL_ONOFF.,1
BC_FUNC_MENUFLAG,是否菜单标志,"",.BC_FUNC_MENUFLAG.,1
BC_FUNC_TRADEFUNSTATE,交易功能状态,"",.BC_FUNC_TRADEFUNSTATE.,1
BC_FUNC_TRADETYPE,交易类型,"",.BC_FUNC_TRADETYPE.,1
BC_FUNC_TYPE,功能类型,"",.BC_FUNC_TYPE.,1
BC_INSTINFO_LVL,机构层级,"",.BC_INSTINFO_LVL.,1
BC_TO_HISTORY,转历史的方式,"",.BC_TO_HISTORY.,1
LEADER_CONFIG,,"",.LEADER_CONFIG.,1
BRANCH_DEPT_LEADER,,LEADER_CONFIG,.BRANCH_DEPT_LEADER.,2
SUB_BRANCH_LEADER,,LEADER_CONFIG,.SUB_BRANCH_LEADER.,2
LEADER_GROUP,,"",.LEADER_GROUP.,1
STAFF_GROUP,,"",.STAFF_GROUP.,1
ORG_GROUP,,"",.ORG_GROUP.,1
BC_UNIT_INT_FLAG,统一外围接口标志,"",.BC_UNIT_INT_FLAG.,1
BC_VERI_TYPE,文件校验类型,"",.BC_VERI_TYPE.,1
简要说明,,日期,作者,
BC_TLR_SEX,性别,"",.BC_TLR_SEX.,1
BC_TLR_SKIN,皮肤,"",.BC_TLR_SKIN.,1
Open_Flag,是否开通,"",.Open_Flag.,1
BC_PROCESS_FLAG,处理状态标志,"",.BC_PROCESS_FLAG.,1
BC_RGTINST_DEALFLAG,短信通知标志/处理标志,"",.BC_RGTINST_DEALFLAG.,1
BC_RGTINST_OPERTYPE,操作类型,"",.BC_RGTINST_OPERTYPE.,1
BC_INSTINFO_SYS,一分类型,"",.BC_INSTINFO_SYS.,1
BC_INSTINFO_TMTYPE,机构终端管理方式,"",.BC_INSTINFO_TMTYPE.,1
BC_FUNC_CHECKFLAG,是否验证权限,"",.BC_FUNC_CHECKFLAG.,1
BC_FUNC_LOGLEVEL,日志级别,"",.BC_FUNC_LOGLEVEL.,1
BC_SYSPMT_ORGMFLAG,是否和机构管理系统互联标志,"",.BC_SYSPMT_ORGMFLAG.,1
BC_SYSPMT_SYSSTAT,系统状态,"",.BC_SYSPMT_SYSSTAT.,1
BC_SYSPMT_TLRMFLAG,是否和柜员管理系统互联标志,"",.BC_SYSPMT_TLRMFLAG.,1
BC_INSTINFO_MDFLAG,是否有监控设备,"",.BC_INSTINFO_MDFLAG.,1
BC_INSTINFO_NETFLAG,联网标志,"",.BC_INSTINFO_NETFLAG.,1
BC_INSTINFO_PAYNETFLAG,是否急付款网点,"",.BC_INSTINFO_PAYNETFLAG.,1
BC_INSTINFO_RMAUFLAG,是否可以远程授权,"",.BC_INSTINFO_RMAUFLAG.,1
BC_INSTINFO_STAT,机构状态,"",.BC_INSTINFO_STAT.,1
License_Flag,,"",.License_Flag.,1
创建文档,,"2019.10.31",刘泽、马玉红,
Main_Flag,维护类型,"",.Main_Flag.,1
Mgr_Flag,主管标志,"",.Mgr_Flag.,1
Node_Flag,所属机构是否为营业机构标志,"",.Node_Flag.,1
ACTION,操作请求,"",.ACTION.,1
BC_MENU_LEAFFLAG,是否叶子节点,"",.BC_MENU_LEAFFLAG.,1
BC_INST_LASTMANAGE,末级管理机构标志,"",.BC_INST_LASTMANAGE.,1
BC_SYS_TYPE9,外系统类型,"9",.BC_SYS_TYPE9.,1
App_Type,应用系统类型,"",.App_Type.,1
BC_APP_APPTYPE,应用类别1,"",.BC_APP_APPTYPE.,1
Sex,性别,"",.Sex.,1
TESTWYP2,测试wyp,,.TESTWYP2.,1
BC_INST_DEALTYPE,交易类型,"",.BC_INST_DEALTYPE.,1
BC_INSTINFO_ABLI,机构职能,"",.BC_INSTINFO_ABLI.,1
BC_INSTINFO_AREAATTR,地域属性,"",.BC_INSTINFO_AREAATTR.,1
BC_INSTINFO_ATTR,机构属性,"",.BC_INSTINFO_ATTR.,1
BC_INSTINFO_BAFLAG,是否与金融机构同址,"",.BC_INSTINFO_BAFLAG.,1
BC_INSTINFO_FLAG,直属标志,"",.BC_INSTINFO_FLAG.,1
BC_PARA_PVALTYPE,参数值类型,"",.BC_PARA_PVALTYPE.,1
BC_FILE_YESNO,文件管理是否标志,"",.BC_FILE_YESNO.,1
BC_FUNC_AUTHFLAG,授权标识,"",.BC_FUNC_AUTHFLAG.,1
Brh_Sub_Type_02,机构类别细分2,Brh_Type,.Brh_Type.Brh_Sub_Type_02.,2
Orgmgr_Flag,是否和机构管理系统互联标志,"",.Orgmgr_Flag.,1
Para_Class,参数分类,"",.Para_Class.,1
Para_State,参数状态,"",.Para_State.,1
Para_Val_Type,参数值类型,"",.Para_Val_Type.,1
 Bussi_Status,业务开关,"",. Bussi_Status.,1
Ca_Flag,柜员统一认证标志,"",.Ca_Flag.,1
Role_Flag,角色标志,"",.Role_Flag.,1
BC_TLR_ENGAKIND,用工性质,"",.BC_TLR_ENGAKIND.,1
BC_TLR_FTFLAG,兼专职类型,"",.BC_TLR_FTFLAG.,1
BC_TLR_LOGINSTAT,人员签到签退状态,"",.BC_TLR_LOGINSTAT.,1
BC_TLR_LOGINSYS,系统允许登陆标志,"",.BC_TLR_LOGINSYS.,1
BC_TLR_LOGINTYPE,登录状态,"",.BC_TLR_LOGINTYPE.,1
Brh_Attr,机构属性,"",.Brh_Attr.,1
Brh_Sub_Type_01,机构类别细分1,Brh_Type,.Brh_Type.Brh_Sub_Type_01.,2
BC_TLR_SUBMITFLAG,提交标识,"",.BC_TLR_SUBMITFLAG.,1
BC_TLR_TLRSTAT,人员状态,"",.BC_TLR_TLRSTAT.,1
BC_TLR_TLRTYPE,人员类别,"",.BC_TLR_TLRTYPE.,1
BC_TLR_WEBSTYLE,页面风格,"",.BC_TLR_WEBSTYLE.,1
BC_MSG_PROCESS_TYPE,消息处理类型,"",.BC_MSG_PROCESS_TYPE.,1
BC_MSG_TYPE,消息类型,"",.BC_MSG_TYPE.,1
BC_PARA_ALLOWMAINTAIN,是否允许维护,"",.BC_PARA_ALLOWMAINTAIN.,1
BC_PARA_EFFECTNOW,参数是否立即生效,"",.BC_PARA_EFFECTNOW.,1
BC_PARA_EFFECTSTATUS,参数生效状态,"",.BC_PARA_EFFECTSTATUS.,1
BC_PARA_PCLASS,参数分类,"",.BC_PARA_PCLASS.,1
BC_APP_OPENFLAG,是否开通,"",.BC_APP_OPENFLAG.,1
BC_APP_PROTOCOLTYPE,协议类型,"",.BC_APP_PROTOCOLTYPE.,1
BC_INSTINFO_TYPE,机构类别,"",.BC_INSTINFO_TYPE.,1
BC_INST_LOGINSTATE,机构签到签退状态,"",.BC_INST_LOGINSTATE.,1
BC_IS_LOGDTL,是否记录中心流水,"",.BC_IS_LOGDTL.,1
BC_JNL_ABFLAG,后台异常处理标志,"",.BC_JNL_ABFLAG.,1
BC_JNL_DCFLAG,借贷标志,"",.BC_JNL_DCFLAG.,1
BC_JNL_TRANSTATE,交易状态,"",.BC_JNL_TRANSTATE.,1
BC_MENU_FUNCGROUPFLAG,是否显示,"",.BC_MENU_FUNCGROUPFLAG.,
BC_CHNL_YESNO,渠道代码是否标识1,"","",1
BC_COMMON_ENABLEFLAG,启用禁用标识,"",.BC_COMMON_ENABLEFLAG.,1
BUSI_Module,,"",.BUSI_Module.,1
Is_Allow_Maintain,是否允许维护,"",.Is_Allow_Maintain.,1
isEmployee,Is Employee1,"",.isEmployee.,1
Leaf_Flag,是否为叶子,"",.Leaf_Flag.,1
Login_Sys,系统允许登陆标志,"",.Login_Sys.,1
Log_Type,日志类型,"",.Log_Type.,1
Role_Type,角色类型,"",.Role_Type.,1
BC_SYS_TYPE11,外系统类型11,"9",.BC_SYS_TYPE9.,1
Deal_Type11,交易类型,"",.Deal_Type11.,1
"1",,"",.1.,1
taskProc,,"",.taskProc.,1
taskConf,,"",.taskConf.,1
taskStatus,,taskProc,.taskStatus.,2
JGKH_ONEBRANCH_GKBM,,"",.JGKH_ONEBRANCH_GKBM.,1
ORG_Unit,,"",.ORG_Unit.,1
TEMPLATE_FILE_ROUTE,,"",.TEMPLATE_FILE_ROUTE.,1
isImportDate,,fileConf,.isImportDate.,2
WX_YWLX,,"",.WX_YWLX.,1
orgRange,,"",.orgRange.,1
scm,,"",.scm.,1
Score_EventType,,"",.Score_EventType.,1
accType,,"",.accType.,1
JXFP_SSCP,,"",.JXFP_SSCP.,1
scm-1,,scm,.scm-1.,2
Score_LeaveType,,"",.Score_LeaveType.,1
ORG_instType,,"",.ORG_instType.,1
Score_Status,,"",.Score_Status.,1
ORG_Currency,,"",.ORG_Currency.,1
JXFP_XYK,,"",.JXFP_XYK.,1
pmfzlx,,"",.pmfzlx.,1
JXFP_LC,,"",.JXFP_LC.,1
Score_ManageType,,"",.Score_ManageType.,1
Score_Fflag,,"",.Score_Fflag.,1
Rpt_Tpl_Type,,"",.Rpt_Tpl_Type.,1
JXFP_YWTX,,"",.JXFP_YWTX.,1
BUSI_Type,,"",.BUSI_Type.,1
BUSINESS_PROCESS,,"",.BUSINESS_PROCESS.,1
JXFP_FPLX,,"",.JXFP_FPLX.,1
JXFP_CPLXDM,,"",.JXFP_CPLXDM.,1
chkOrigin,,taskConf,.chkOrigin.,2
chkFlagProc,,taskProc,.chkFlagProc.,2
fileConf,,"",.fileConf.,1
fileType,,fileConf,.fileType.,2
operType,,fileConf,.operType.,2
"12",,"","1",
ORG_instKinds,,"",.ORG_instKinds.,1
handleMethod,,fileConf,.handleMethod.,2
isCover,,fileConf,.isCover.,2
isExecute,,fileConf,.isExecute.,2
status,,fileConf,.status.,2
isUserId,,fileConf,.isUserId.,2
Tlr_Type,人员类别,"",.Tlr_Type.,1
BC_SYS_TYPE12,外系统类型12,"",.BC_SYS_TYPE.,1
BC_SYS_TYPE99,,"99",.BC_SYS_TYPE9.,19
BC_INSTINFO_STYLE,金融机构类型,"",.BC_INSTINFO_STYLE.,1
BC_INSTINFO_SUB_TYPE_01,机构类别细分1,BC_INSTINFO_TYPE,.BC_INSTINFO_TYPE.BC_INSTINFO_SUB_TYPE_01.,2
BC_INSTINFO_SUB_TYPE_02,机构类别细分2,BC_INSTINFO_TYPE,.BC_INSTINFO_TYPE.BC_INSTINFO_SUB_TYPE_02.,2
BC_INSTINFO_SUB_TYPE_03,机构类别细分3,BC_INSTINFO_TYPE,.BC_INSTINFO_TYPE.BC_INSTINFO_SUB_TYPE_03.,2
BC_INSTINFO_SUB_TYPE_04,机构类别细分4,BC_INSTINFO_TYPE,.BC_INSTINFO_TYPE.BC_INSTINFO_SUB_TYPE_04.,2
BC_INSTINFO_SUB_TYPE_05,机构类别细分5,BC_INSTINFO_TYPE,.BC_INSTINFO_TYPE.BC_INSTINFO_SUB_TYPE_05.,2
Brh_Type,机构类别,"",.Brh_Type.,1
BC_ROLE_DEPTTYPE,所属部门类型,"",.BC_ROLE_DEPTTYPE.,1
BC_ROLE_ENABLEFLAG,有效标识,"",.BC_ROLE_ENABLEFLAG.,1
BC_ROLE_MGRFLAG,主管标志,"",.BC_ROLE_MGRFLAG.,1
BC_ROLE_NODEFLAG,所属机构是否为营业机构标志,"",.BC_ROLE_NODEFLAG.,1
BC_SEND_MSG_FLAG,上传文件是否通知共享,"",.BC_SEND_MSG_FLAG.,1
Pay_Channel,缴费渠道,"",.Pay_Channel.,1
Pay_Mode,缴费方式,"",.Pay_Mode.,1
Pay_Status,缴费状态,"",.Pay_Status.,1
province,Province,country,.country.province.,2
RESULT_STATUS,结果状态,"",.RESULT_STATUS.,1
Tlrmgr_Flag,是否和柜员管理系统互联标志,"",.Tlrmgr_Flag.,1
Tlr_Stat,人员状态,"",.Tlr_Stat.,1
Enable_Flag,有效标识,"",.Enable_Flag.,1
Engage_Kind,用工性质,"",.Engage_Kind.,1
FLOW_OPTIONS,流程选项,"",.FLOW_OPTIONS.,1
Brh_Sub_Type_03,机构类别细分3,Brh_Type,.Brh_Type.Brh_Sub_Type_03.,2
COF_FUNCTYPE,功能类型,"",.COF_FUNCTYPE.,1
Brh_Sub_Type_04,机构类别细分4,Brh_Type,.Brh_Type.Brh_Sub_Type_04.,2
Brh_Sub_Type_05,机构类别细分5,Brh_Type,.Brh_Type.Brh_Sub_Type_05.,2
Sys_On_Off,系统开关,"",.Sys_On_Off.,1
Sys_State,系统状态,"",.Sys_State.,1
BC_JNL_TXSTATE,交易状态,"",.BC_JNL_TXSTATE.,1
Brh_Sys,一分类型,"",.Brh_Sys.,1
city,City,province,.country.province.city.,3
Intfc_Type,,"",.Intfc_Type.,1
Message_Format,,"",.Message_Format.,1
FLOW_STATUS,流程状态,"",.FLOW_STATUS.,1
Func_Group_Flag,是否显示菜单,"",.Func_Group_Flag.,
industry,industry,"",.industry.,1
Inst_Level,所属机构层级,"",.Inst_Level.,1
Request_Protocol,,"",.Request_Protocol.,1
Request_Style,,"",.Request_Style.,1
Inst_State,机构营业状态,"",.Inst_State.,1
Inst_Type,金融机构类型,"",.Inst_Type.,1
BC_SYS_TYPE13,外系统类型13,"",.BC_SYS_TYPE9.,1
BC_SYS_TYPE10,外系统10类型,"",.BC_SYS_TYPE.,1
BC_TLR_EDUBG,学历,"",.BC_TLR_EDUBG.,1
Brh_Abli,机构职能,"",.Brh_Abli.,1
BC_TLR_MAINFLAG,维护类型,"",.BC_TLR_MAINFLAG.,1
BC_TLR_POST,岗位,"",.BC_TLR_POST.,1
